#!/bin/bash

echo "🚀 启动OCR服务器..."
echo ""

# 确保日志目录存在
mkdir -p "./logs"

# 解析命令行参数
USE_GPU_MODE=false
USE_DAEMON_MODE=true  # 默认使用daemon模式
INFERENCE_ENGINE="openvino"  # 默认使用OpenVINO引擎
CPU_THREADS=""  # CPU线程数，默认为空（使用所有核心）

while [[ $# -gt 0 ]]; do
    case $1 in
        --gpu)
            USE_GPU_MODE=true
            shift
            ;;
        --cpu)
            USE_GPU_MODE=false
            shift
            ;;
        -ie|--inference_engine)
            if [[ -n "$2" && "$2" != --* ]]; then
                INFERENCE_ENGINE="$2"
                shift 2
            else
                echo "错误: --inference_engine 需要指定值 (onnx 或 openvino)"
                exit 1
            fi
            ;;
        -fg|--foreground)
            USE_DAEMON_MODE=false
            shift
            ;;
        --cpu_threads)
            if [[ -n "$2" && "$2" != --* ]]; then
                CPU_THREADS="$2"
                shift 2
            else
                echo "错误: --cpu_threads 需要指定数值"
                exit 1
            fi
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --cpu                       使用CPU模式启动 (默认)"
            echo "  --gpu                       使用GPU模式启动"
            echo "  --inference_engine <值>     推理引擎类型 (onnx|openvino，默认: openvino)"
            echo "  --cpu_threads <数值>        CPU线程数 (默认: 使用所有核心)"
            echo "  -fg, --foreground           前台运行模式"
            echo "  -h, --help                  显示此帮助信息"
            echo ""
            echo "示例:"
            echo "  $0                                    # CPU模式后台运行 (OpenVINO，使用所有核心)"
            echo "  $0 --gpu                              # GPU模式后台运行 (OpenVINO)"
            echo "  $0 --inference_engine onnx            # CPU模式后台运行 (ONNX，使用所有核心)"
            echo "  $0 --cpu_threads 16                   # CPU模式后台运行 (OpenVINO，使用16个线程)"
            echo "  $0 --gpu --inference_engine onnx      # GPU模式后台运行 (ONNX)"
            echo "  $0 --foreground                       # CPU模式前台运行 (OpenVINO)"
            echo "  $0 --cpu_threads 32 --inference_engine onnx # CPU模式后台运行 (ONNX，使用32个线程)"
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            echo "使用 $0 --help 查看帮助"
            exit 1
            ;;
    esac
done

# 验证推理引擎参数
if [[ "$INFERENCE_ENGINE" != "onnx" && "$INFERENCE_ENGINE" != "openvino" ]]; then
    echo "错误: 推理引擎必须是 'onnx' 或 'openvino'"
    exit 1
fi

# 统一使用9000端口，根据模式设置不同的Worker类
BIND_ADDRESS="0.0.0.0:9000"

# 构建推理引擎参数
INFERENCE_ENGINE_ARGS=""
if [[ "$USE_GPU_MODE" == "true" ]]; then
    INFERENCE_ENGINE_ARGS="$INFERENCE_ENGINE_ARGS --use_gpu"
fi
INFERENCE_ENGINE_ARGS="$INFERENCE_ENGINE_ARGS --inference_engine $INFERENCE_ENGINE"

if [[ "$USE_GPU_MODE" == "true" ]]; then
    echo "💻 使用GPU模式启动..."
    WORKER_CLASS="custom_worker.GPUMonitoringWorker"
else
    echo "🖥️  使用CPU模式启动..."
    WORKER_CLASS="uvicorn.workers.UvicornWorker"
fi

if [[ "$USE_DAEMON_MODE" == "true" ]]; then
    echo "🔧 运行模式: 后台运行 (daemon)"
    DAEMON_ARGS="--daemon --pid ./gunicorn.pid"
else
    echo "🔧 运行模式: 前台运行 (foreground)"
    DAEMON_ARGS=""
fi

echo "📋 配置信息:"
echo "  绑定地址: $BIND_ADDRESS"
echo "  Worker类: $WORKER_CLASS"
echo "  工作进程数: 4"
echo "  运行模式: $([ "$USE_DAEMON_MODE" == "true" ] && echo "后台" || echo "前台")"
echo "  推理引擎: $INFERENCE_ENGINE"
echo "  GPU模式: $([ "$USE_GPU_MODE" == "true" ] && echo "开启" || echo "关闭")"
echo "  引擎参数: $INFERENCE_ENGINE_ARGS"
echo ""

# 构建gunicorn命令
GUNICORN_CMD="gunicorn ocr_server:ocr_server \
    --bind \"$BIND_ADDRESS\" \
    --workers 4 \
    --worker-class \"$WORKER_CLASS\" \
    --log-level info \
    --timeout 30 \
    --access-logfile \"./logs/gunicorn-access.log\" \
    --error-logfile \"./logs/gunicorn-error.log\" \
    $DAEMON_ARGS"

# 设置环境变量来传递参数给Python脚本
export OCR_SERVER_ARGS="$INFERENCE_ENGINE_ARGS"

# 设置CPU线程数环境变量
if [[ -n "$CPU_THREADS" ]]; then
    if [[ "$INFERENCE_ENGINE" == "openvino" ]]; then
        export OPENVINO_CPU_THREADS="$CPU_THREADS"
        echo "  OpenVINO CPU线程数: $CPU_THREADS"
    elif [[ "$INFERENCE_ENGINE" == "onnx" ]]; then
        export ONNX_CPU_THREADS="$CPU_THREADS"
        echo "  ONNX CPU线程数: $CPU_THREADS"
    fi
else
    echo "  CPU线程数: 使用所有可用核心"
fi

if [[ "$USE_DAEMON_MODE" == "true" ]]; then
    # 后台模式：启动后检查结果
    eval $GUNICORN_CMD
    
    if [ $? -eq 0 ]; then
        echo "✅ OCR服务器启动成功！"
        echo "   服务地址: http://$BIND_ADDRESS"
        echo "   日志文件: ./logs/"
        echo "   进程ID文件: ./gunicorn.pid"
        echo "   使用 './stop.sh' 停止服务器"
    else
        echo "❌ OCR服务器启动失败"
    fi
else
    # 前台模式：显示启动信息后直接执行
    echo "✅ 正在启动OCR服务器..."
    echo "   服务地址: http://$BIND_ADDRESS"
    echo "   日志文件: ./logs/"
    echo "   前台运行中，按 Ctrl+C 停止服务器"
    echo ""
    echo "📡 服务器日志输出："
    echo "----------------------------------------"
    
    # 直接执行gunicorn（会阻塞终端）
    eval $GUNICORN_CMD
fi 