#!/bin/bash
# Tesseract离线安装包制作脚本
# 在有网络的机器上运行此脚本来准备离线安装包

set -e

# 无颜色输出

# 检测操作系统
detect_os() {
    if [[ "$OSTYPE" == "linux"* ]]; then
        if [ -f /etc/debian_version ]; then
            echo "debian"
        elif [ -f /etc/redhat-release ]; then
            echo "rhel"
        elif [ -f /etc/SuSE-release ] || [ -f /etc/SUSE-brand ]; then
            echo "suse"
        elif grep -qE "(ID|ID_LIKE)=\"?suse\"?|SUSE|openSUSE" /etc/os-release 2>/dev/null; then
            echo "suse"
        else
            echo "linux"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macos"
    else
        echo "unknown"
    fi
}

# 检查网络连接
check_internet() {
    ping -c 1 8.8.8.8 >/dev/null 2>&1
}

# 制作Ubuntu/Debian离线包
create_debian_package() {
    echo "📦 制作Ubuntu/Debian离线安装包..."
    
    local package_dir="tesseract-offline-debian"
    mkdir -p "$package_dir"
    cd "$package_dir"
    
    echo "下载Tesseract核心包..."
    apt download tesseract-ocr tesseract-ocr-chi-sim tesseract-ocr-chi-tra
    
    echo "下载核心依赖包..."
    apt download libtesseract4 libtesseract-data liblept5
    
    echo "下载其他依赖包..."
    apt-cache depends tesseract-ocr | grep "Depends:" | awk '{print $2}' | xargs -I {} apt download {} || true
    
    cd ..
    echo "打包压缩..."
    tar -czf tesseract-offline-debian.tar.gz "$package_dir"
    rm -rf "$package_dir"
    
    echo "✅ Ubuntu/Debian离线包已创建: tesseract-offline-debian.tar.gz"
}

# 制作CentOS/RHEL离线包
create_rhel_package() {
    echo "📦 制作CentOS/RHEL离线安装包..."
    
    local package_dir="tesseract-offline-rhel"
    mkdir -p "$package_dir"
    cd "$package_dir"
    
    # 确保EPEL仓库可用
    if ! rpm -q epel-release >/dev/null 2>&1; then
        echo "安装EPEL仓库..."
        yum install -y epel-release || dnf install -y epel-release
    fi
    
    echo "下载Tesseract包及依赖..."
    if command -v dnf >/dev/null 2>&1; then
        dnf download --resolve tesseract tesseract-langpack-chi_sim tesseract-langpack-chi_tra
    else
        yumdownloader --resolve tesseract tesseract-langpack-chi_sim tesseract-langpack-chi_tra
    fi
    
    cd ..
    echo "打包压缩..."
    tar -czf tesseract-offline-rhel.tar.gz "$package_dir"
    rm -rf "$package_dir"
    
    echo "✅ CentOS/RHEL离线包已创建: tesseract-offline-rhel.tar.gz"
}

# 制作SUSE离线包
create_suse_package() {
    echo "📦 制作SUSE离线安装包..."
    
    local package_dir="tesseract-offline-suse"
    mkdir -p "$package_dir"
    cd "$package_dir"
    
    echo "清理之前的缓存包..."
    zypper clean --all
    
    echo "下载Tesseract及其依赖包..."
    zypper download tesseract-ocr tesseract-ocr-traineddata-osd libtesseract4

    echo "复制下载的包文件..."
    # 遍历所有架构目录复制rpm包
    find /var/cache/zypp/packages/openSUSE-Aliyun-OSS -name "*.rpm" -exec cp {} . \;
    
    echo "当前目录包文件列表:"
    ls -la *.rpm
    
    cd ..
    echo "打包压缩..."
    tar -czf tesseract-offline-suse.tar.gz "$package_dir"
    rm -rf "$package_dir"
    
    echo "✅ SUSE离线包已创建: tesseract-offline-suse.tar.gz"
}

# 主函数
main() {
    echo "🚀 开始制作Tesseract离线安装包..."
    
    if ! check_internet; then
        echo "❌ 需要网络连接来制作离线安装包"
        exit 1
    fi
    
    local os_type=$(detect_os)
    echo "🖥️ 检测到操作系统: $os_type"
    
    case "$os_type" in
        "debian")
            create_debian_package
            ;;
        "rhel")
            create_rhel_package
            ;;
        "suse")
            create_suse_package
            ;;
        *)
            echo "⚠️ 当前系统不支持自动制作离线包"
            echo "支持的系统: Ubuntu/Debian, CentOS/RHEL, SUSE/openSUSE"
            exit 1
            ;;
    esac
    
    echo "🎉 离线安装包制作完成！"
    echo "将 tesseract-offline-*.tar.gz 文件与OCR服务包放在同一目录下"
    echo "然后运行 ./install.sh 进行离线安装"
}

main "$@" 