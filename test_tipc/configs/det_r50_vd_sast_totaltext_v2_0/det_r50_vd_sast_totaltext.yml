Global:
  use_gpu: true
  epoch_num: 5000
  log_smooth_window: 20
  print_batch_step: 2
  save_model_dir: ./output/sast_r50_vd_tt/
  save_epoch_step: 1000
  # evaluation is run every 5000 iterations after the 4000th iteration
  eval_batch_step: [4000, 5000]
  cal_metric_during_train: False
  pretrained_model: ./pretrain_models/ResNet50_vd_ssld_pretrained
  checkpoints: 
  save_inference_dir:
  use_visualdl: False
  infer_img: 
  save_res_path: ./output/sast_r50_vd_tt/predicts_sast.txt

Architecture:
  model_type: det
  algorithm: SAST
  Transform:
  Backbone:
    name: ResNet_SAST
    layers: 50
  Neck:
    name: SASTFPN
    with_cab: True
  Head:
    name: SASTHead

Loss:
  name: SASTLoss
  
Optimizer:
  name: Adam
  beta1: 0.9
  beta2: 0.999
  lr:
  #  name: Cosine
    learning_rate: 0.001
  #  warmup_epoch: 0
  regularizer:
    name: 'L2'
    factor: 0

PostProcess:
  name: SASTPostProcess
  score_thresh: 0.5
  sample_pts_num: 6
  nms_thresh: 0.2
  expand_scale: 1.2
  shrink_ratio_of_width: 0.2

Metric:
  name: DetMetric
  main_indicator: hmean

Train:
  dataset:
    name: SimpleDataSet
    data_dir: ./train_data/total_text/train
    label_file_list: [./train_data/total_text/train/train.txt]
    ratio_list: [1.0]
    transforms:
      - DecodeImage: # load image
          img_mode: BGR
          channel_first: False
      - DetLabelEncode: # Class handling label
      - SASTProcessTrain:
          image_shape: [512, 512]
          min_crop_side_ratio: 0.3
          min_crop_size: 24
          min_text_size: 4
          max_text_size: 512
      - KeepKeys:
          keep_keys: ['image', 'score_map', 'border_map', 'training_mask', 'tvo_map', 'tco_map'] # dataloader will return list in this order
  loader:
    shuffle: True
    drop_last: False
    batch_size_per_card: 4
    num_workers: 4

Eval:
  dataset:
    name: SimpleDataSet
    data_dir: ./train_data/
    label_file_list: 
      - ./train_data/total_text/test/test.txt
    transforms:
      - DecodeImage: # load image
          img_mode: BGR
          channel_first: False
      - DetLabelEncode: # Class handling label
      - DetResizeForTest:
          resize_long: 768
      - NormalizeImage:
          scale: 1./255.
          mean: [0.485, 0.456, 0.406]
          std: [0.229, 0.224, 0.225]
          order: 'hwc'
      - ToCHWImage:
      - KeepKeys:
          keep_keys: ['image', 'shape', 'polys', 'ignore_tags']
  loader:
    shuffle: False
    drop_last: False
    batch_size_per_card: 1 # must be 1
    num_workers: 2
