===========================serving_params===========================
model_name:ch_ppocr_mobile_v2_0_rec_PACT
python:python3.7
trans_model:-m paddle_serving_client.convert
--det_dirname:null
--model_filename:inference.pdmodel
--params_filename:inference.pdiparams
--det_serving_server:null
--det_serving_client:null
--rec_dirname:./inference/ch_ppocr_mobile_v2.0_rec_pact_infer/
--rec_serving_server:./deploy/pdserving/ppocr_rec_mobile_pact_serving/
--rec_serving_client:./deploy/pdserving/ppocr_rec_mobile_pact_client/
serving_dir:./deploy/pdserving
web_service:web_service_rec.py --config=config.yml --opt op.rec.concurrency="1"
op.det.local_service_conf.devices:gpu|null
op.det.local_service_conf.use_mkldnn:False
op.det.local_service_conf.thread_num:6
op.det.local_service_conf.use_trt:False
op.det.local_service_conf.precision:fp32
op.det.local_service_conf.model_config:
op.rec.local_service_conf.model_config:
pipline:pipeline_http_client.py --det=False
--image_dir:../../inference/rec_inference
