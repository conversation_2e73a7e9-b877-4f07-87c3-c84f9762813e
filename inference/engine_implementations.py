import os
import onnxruntime
import openvino as ov
import numpy as np
from typing import List, Dict, Any
from .engine_interface import InferenceEngine
from .conditional_engine import EngineRegistry


class ONNXEngine(InferenceEngine):
    """ONNX推理引擎实现"""
    
    def __init__(self, model_path: str, use_gpu: bool = False, **kwargs):
        super().__init__(model_path, use_gpu, **kwargs)
        self.session = None
        
    def initialize(self) -> None:
        """初始化ONNX推理引擎"""
        # 选择执行提供者
        if self.use_gpu:
            providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
        else:
            providers = ['CPUExecutionProvider']
        
        # 配置会话选项
        session_options = onnxruntime.SessionOptions()
        
        # 配置CPU线程数（仅在CPU模式下）
        if not self.use_gpu:
            # 获取CPU线程数配置，优先级：kwargs > 环境变量 > 默认值
            cpu_threads = self.kwargs.get('cpu_threads', None)
            if cpu_threads is None:
                cpu_threads = os.environ.get('ONNX_CPU_THREADS', None)
                if cpu_threads is not None:
                    cpu_threads = int(cpu_threads)
                else:
                    # 默认使用所有可用核心
                    cpu_threads = os.cpu_count()
            
            # 设置ONNX Runtime的线程配置
            session_options.intra_op_num_threads = cpu_threads  # 单个操作内的并行度
            session_options.inter_op_num_threads = 1  # 操作间的并行度（通常设为1）
            session_options.execution_mode = onnxruntime.ExecutionMode.ORT_PARALLEL
            session_options.graph_optimization_level = onnxruntime.GraphOptimizationLevel.ORT_ENABLE_ALL
        
        # 创建推理会话
        filtered_kwargs = {k: v for k, v in self.kwargs.items() if k != 'cpu_threads'}
        self.session = onnxruntime.InferenceSession(
            self.model_path, 
            providers=providers,
            sess_options=session_options,
            **filtered_kwargs
        )
        
        # 获取输入输出名称
        self.input_names = [node.name for node in self.session.get_inputs()]
        self.output_names = [node.name for node in self.session.get_outputs()]
        
    def predict(self, inputs: np.ndarray) -> List[np.ndarray]:
        """执行ONNX推理"""
        if self.session is None:
            raise RuntimeError("Engine not initialized. Call initialize() first.")
        
        # 构建输入字典
        input_feed = {}
        if isinstance(inputs, dict):
            input_feed = inputs
        else:
            # 如果是单个数组，使用第一个输入名称
            input_feed = {self.input_names[0]: inputs}
        
        # 执行推理
        outputs = self.session.run(self.output_names, input_feed)
        return outputs
    
    def get_input_names(self) -> List[str]:
        """获取输入层名称"""
        return self.input_names
    
    def get_output_names(self) -> List[str]:
        """获取输出层名称"""
        return self.output_names
    
    @property
    def engine_type(self) -> str:
        """返回引擎类型"""
        return "ONNX"


class OpenVINOEngine(InferenceEngine):
    """OpenVINO推理引擎实现"""
    
    def __init__(self, model_path: str, use_gpu: bool = False, **kwargs):
        super().__init__(model_path, use_gpu, **kwargs)
        self.compiled_model = None
        
    def initialize(self) -> None:
        """初始化OpenVINO推理引擎"""
        # 选择设备
        device = "GPU" if self.use_gpu else "CPU"
        
        # 创建Core对象
        core = ov.Core()
        
        # 配置CPU设备属性（仅在CPU模式下）
        if not self.use_gpu:
            # 获取CPU线程数配置，优先级：kwargs > 环境变量 > 默认值
            cpu_threads = self.kwargs.get('cpu_threads', None)
            if cpu_threads is None:
                cpu_threads = os.environ.get('OPENVINO_CPU_THREADS', None)
                if cpu_threads is not None:
                    cpu_threads = int(cpu_threads)
                else:
                    # 默认使用所有可用核心
                    cpu_threads = os.cpu_count()
            
            # 设置CPU设备属性
            core.set_property(device, {
                ov.properties.inference_num_threads(): cpu_threads,
                ov.properties.enable_profiling(): False,
                # 启用CPU扩展以获得更好的性能
                ov.properties.hint.enable_cpu_pinning(): True,
                # 设置调度核心类型（使用性能核心）
                ov.properties.hint.scheduling_core_type(): ov.properties.hint.SchedulingCoreType.PCORE_ONLY,
                # 启用超线程
                ov.properties.hint.enable_hyper_threading(): True,
                # 设置执行模式为吞吐量优化
                ov.properties.hint.execution_mode(): ov.properties.hint.ExecutionMode.PERFORMANCE
            })
        
        # 编译模型
        self.compiled_model = core.compile_model(self.model_path, device)
        
        # 获取输入输出信息
        self.input_names = [inp.get_any_name() for inp in self.compiled_model.inputs]
        self.output_names = [out.get_any_name() for out in self.compiled_model.outputs]
        
    def predict(self, inputs: np.ndarray) -> List[np.ndarray]:
        """执行OpenVINO推理"""
        if self.compiled_model is None:
            raise RuntimeError("Engine not initialized. Call initialize() first.")
        
        # 执行推理
        if isinstance(inputs, dict):
            result = self.compiled_model(inputs)
        else:
            # 如果是单个数组，直接传递
            result = self.compiled_model(inputs)
        
        # 转换为列表格式
        if hasattr(result, 'values'):
            return list(result.values())
        elif isinstance(result, dict):
            return list(result.values())
        else:
            return [result]
    
    def get_input_names(self) -> List[str]:
        """获取输入层名称"""
        return self.input_names
    
    def get_output_names(self) -> List[str]:
        """获取输出层名称"""
        return self.output_names
    
    @property
    def engine_type(self) -> str:
        """返回引擎类型"""
        return "OpenVINO"


# 直接注册引擎
EngineRegistry.register_engine("onnx", ONNXEngine)
EngineRegistry.register_engine("openvino", OpenVINOEngine)