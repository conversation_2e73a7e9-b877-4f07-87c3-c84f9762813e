from paddleocr import PPStructureV3

from paddleocr import DocImgOrientationClassification

model = DocImgOrientationClassification(model_name="PP-LCNet_x1_0_doc_ori")
output = model.predict("/Users/<USER>/Coding/rh-ocr/cut-images/2025/07/25/rotate_11_00_55.png",  batch_size=1)
for res in output:
    res.print(json_format=False)

# pipeline = PPStructureV3(use_doc_orientation_classify=False,
#                          use_textline_orientation=False,
#                          device='cpu')
#
# output = pipeline.predict('/Users/<USER>/Downloads/OCR数据集/一附院/脑电图/检查所见及结论.png')
# for res in output:
#     res.print() ## 打印预测的结构化输出
#     res.save_to_json(save_path="output") ## 保存当前图像的结构化json结果
#     res.save_to_markdown(save_path="output") ## 保存当前图像的markdown格式的结果
