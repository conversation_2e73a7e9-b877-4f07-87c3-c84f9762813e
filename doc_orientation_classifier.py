import cv2
import numpy as np
import onnxruntime as ort
import time
from typing import Dict, <PERSON><PERSON>, Optional
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class DocOrientationClassifier:
    """
    基于ONNX的文档图像方向分类器
    替换Tesseract OSD的旋转检测方案
    
    支持的角度：0°, 90°, 180°, 270°
    """
    
    def __init__(self, 
                 model_path: str = "inference/models/ppstructurev3/doc_img_cls.onnx",
                 confidence_threshold: float = 0.5,
                 input_size: Tuple[int, int] = (224, 224)):
        """
        初始化文档方向分类器
        
        Args:
            model_path: ONNX模型路径
            confidence_threshold: 置信度阈值
            input_size: 模型输入尺寸 (height, width)
        """
        self.model_path = model_path
        self.confidence_threshold = confidence_threshold
        self.input_size = input_size
        
        # 类别标签映射 (与PaddleOCR保持一致)
        self.label_map = {
            0: "0",      # 0度
            1: "90",     # 90度  
            2: "180",    # 180度
            3: "270"     # 270度
        }
        
        # 角度映射
        self.angle_map = {
            0: 0,    # 0度
            1: 90,   # 90度
            2: 180,  # 180度
            3: 270   # 270度
        }
        
        # 图像预处理参数 (与PaddleOCR保持一致)
        self.mean = np.array([0.485, 0.456, 0.406], dtype=np.float32)
        self.std = np.array([0.229, 0.224, 0.225], dtype=np.float32)
        
        # 初始化ONNX Runtime会话
        self._init_onnx_session()
        
        logger.info(f"文档方向分类器初始化完成 - 模型: {model_path}")
        logger.info(f"输入尺寸: {input_size}, 置信度阈值: {confidence_threshold}")
    
    def _init_onnx_session(self):
        """初始化ONNX Runtime推理会话"""
        try:
            # 检查模型文件是否存在
            if not Path(self.model_path).exists():
                raise FileNotFoundError(f"ONNX模型文件不存在: {self.model_path}")
            
            # 配置ONNX Runtime
            providers = ['CPUExecutionProvider']
            sess_options = ort.SessionOptions()
            sess_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
            
            # 创建推理会话
            self.session = ort.InferenceSession(
                self.model_path,
                sess_options=sess_options,
                providers=providers
            )
            
            # 获取输入输出信息
            self.input_name = self.session.get_inputs()[0].name
            self.output_name = self.session.get_outputs()[0].name
            
            logger.info(f"ONNX模型加载成功 - 输入: {self.input_name}, 输出: {self.output_name}")
            
        except Exception as e:
            logger.error(f"ONNX模型初始化失败: {e}")
            raise
    
    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        图像预处理
        
        Args:
            image: 输入图像 (BGR格式)
            
        Returns:
            预处理后的图像张量
        """
        # 转换为RGB
        if len(image.shape) == 3 and image.shape[2] == 3:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Resize到模型输入尺寸
        height, width = self.input_size
        resized = cv2.resize(image, (width, height), interpolation=cv2.INTER_LINEAR)
        
        # 归一化到[0,1]
        normalized = resized.astype(np.float32) / 255.0
        
        # 标准化 (ImageNet标准)
        normalized = (normalized - self.mean) / self.std
        
        # 转换为CHW格式并添加batch维度
        transposed = normalized.transpose(2, 0, 1)  # HWC -> CHW
        batched = np.expand_dims(transposed, axis=0)  # CHW -> NCHW
        
        return batched
    
    def _postprocess_output(self, output: np.ndarray) -> Tuple[int, float, str]:
        """
        后处理模型输出
        
        Args:
            output: 模型输出logits
            
        Returns:
            (class_id, confidence, label_name)
        """
        # Softmax
        exp_output = np.exp(output - np.max(output, axis=1, keepdims=True))
        probabilities = exp_output / np.sum(exp_output, axis=1, keepdims=True)
        
        # 获取最高置信度的类别
        class_id = np.argmax(probabilities[0])
        confidence = probabilities[0][class_id]
        label_name = self.label_map[class_id]
        
        return int(class_id), float(confidence), label_name
    
    def predict(self, image: np.ndarray) -> Dict:
        """
        预测图像方向
        
        Args:
            image: 输入图像 (BGR格式)
            
        Returns:
            预测结果字典，格式与rotation_detector兼容
        """
        start_time = time.time()
        
        try:
            # 预处理
            preprocessed = self._preprocess_image(image)
            
            # 推理
            inference_start = time.time()
            outputs = self.session.run([self.output_name], {self.input_name: preprocessed})
            inference_time = time.time() - inference_start
            
            # 后处理
            class_id, confidence, label_name = self._postprocess_output(outputs[0])
            angle = self.angle_map[class_id]
            
            processing_time = time.time() - start_time
            
            # 检查置信度
            if confidence < self.confidence_threshold:
                return {
                    'success': False,
                    'error_msg': f"置信度过低: {confidence:.3f} < {self.confidence_threshold}",
                    'processing_time': processing_time,
                    'inference_time': inference_time,
                    'angle': 0,  # 默认角度
                    'confidence': confidence
                }
            
            return {
                'success': True,
                'angle': angle,
                'confidence': confidence,
                'class_id': class_id,
                'label_name': label_name,
                'processing_time': processing_time,
                'inference_time': inference_time,
                'error_msg': None,
                'optimization_used': {
                    'level': 'onnx',
                    'image_resized': True,
                    'roi_detected': False,
                    'preprocessing_applied': True
                }
            }
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"文档方向预测失败: {e}")
            return {
                'success': False,
                'error_msg': f"预测失败: {str(e)}",
                'processing_time': processing_time,
                'angle': 0,
                'confidence': 0.0
            }
    
    def detect_rotation(self, image: np.ndarray) -> Dict:
        """
        检测图像旋转角度 (与RotationDetector接口兼容)
        
        Args:
            image: 输入图像
            
        Returns:
            检测结果字典
        """
        return self.predict(image)


# 便捷函数
def detect_doc_orientation(image: np.ndarray, 
                          model_path: str = "inference/models/ppstructurev3/doc_img_cls.onnx",
                          confidence_threshold: float = 0.5) -> Dict:
    """
    便捷函数：检测文档图像方向
    
    Args:
        image: 输入图像
        model_path: ONNX模型路径
        confidence_threshold: 置信度阈值
        
    Returns:
        检测结果字典
    """
    classifier = DocOrientationClassifier(
        model_path=model_path,
        confidence_threshold=confidence_threshold
    )
    return classifier.detect_rotation(image)


if __name__ == "__main__":
    # 测试代码
    import sys
    
    if len(sys.argv) < 2:
        print("用法: python doc_orientation_classifier.py <图像路径> [置信度阈值]")
        print("置信度阈值: 0.0-1.0 (默认: 0.5)")
        sys.exit(1)
    
    image_path = sys.argv[1]
    confidence_threshold = float(sys.argv[2]) if len(sys.argv) > 2 else 0.5
    
    try:
        # 加载图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"无法加载图像: {image_path}")
            sys.exit(1)
        
        print(f"测试文档方向分类器")
        print(f"图像: {image_path}")
        print(f"置信度阈值: {confidence_threshold}")
        print("=" * 50)
        
        # 创建分类器
        classifier = DocOrientationClassifier(confidence_threshold=confidence_threshold)
        
        # 预测
        result = classifier.predict(image)
        
        if result['success']:
            print(f"✅ 检测成功")
            print(f"   旋转角度: {result['angle']}°")
            print(f"   置信度: {result['confidence']:.3f}")
            print(f"   类别ID: {result['class_id']}")
            print(f"   标签: {result['label_name']}")
            print(f"   处理时间: {result['processing_time']:.3f}s")
            print(f"   推理时间: {result['inference_time']:.3f}s")
        else:
            print(f"❌ 检测失败: {result['error_msg']}")
            print(f"   处理时间: {result['processing_time']:.3f}s")
            
    except Exception as e:
        print(f"测试失败: {e}")
        sys.exit(1)
