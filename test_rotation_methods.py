#!/usr/bin/env python3
"""
测试不同旋转检测方法的性能和准确性
"""

import cv2
import time
import numpy as np
from rotation_detector import RotationDetector
from inference.predict_ori import create_orientation_classifier

def test_rotation_methods(image_path: str):
    """
    测试Tesseract OSD和PaddleOCR两种旋转检测方法
    
    Args:
        image_path: 测试图像路径
    """
    print(f"🧪 测试旋转检测方法")
    print(f"图像: {image_path}")
    print("=" * 60)
    
    # 加载图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"❌ 无法加载图像: {image_path}")
        return
    
    print(f"图像尺寸: {image.shape}")
    print()
    
    # 测试Tesseract OSD方法
    print("📊 测试 Tesseract OSD 方法")
    print("-" * 30)
    try:
        tesseract_detector = RotationDetector(
            confidence_threshold=1.0,
            optimization_level="high",
            max_image_size=1000,
            enable_roi_detection=False
        )
        
        start_time = time.time()
        tesseract_result = tesseract_detector.detect_rotation(image)
        tesseract_time = time.time() - start_time
        
        if tesseract_result['success']:
            print(f"✅ Tesseract检测成功")
            print(f"   旋转角度: {tesseract_result['angle']}°")
            print(f"   置信度: {tesseract_result['confidence']:.3f}")
            print(f"   总时间: {tesseract_time:.3f}s")
            if 'osd_time' in tesseract_result:
                print(f"   OSD时间: {tesseract_result['osd_time']:.3f}s")
            if 'optimization_used' in tesseract_result:
                opt = tesseract_result['optimization_used']
                print(f"   优化级别: {opt['level']}")
        else:
            print(f"❌ Tesseract检测失败: {tesseract_result['error_msg']}")
            print(f"   总时间: {tesseract_time:.3f}s")
            
    except Exception as e:
        print(f"❌ Tesseract初始化失败: {e}")
        tesseract_result = None
    
    print()
    
    # 测试PaddleOCR方法
    print("📊 测试 PaddleOCR 文档方向分类器 (inference/predict_ori.py)")
    print("-" * 30)
    try:
        paddleocr_classifier = create_orientation_classifier(
            model_path="inference/models/ppstructurev3/doc_img_cls.onnx",
            inference_engine="onnx",
            use_gpu=False,
            confidence_threshold=0.3
        )
        
        start_time = time.time()
        paddleocr_result = paddleocr_classifier.detect_rotation(image)
        paddleocr_time = time.time() - start_time
        
        if paddleocr_result['success']:
            print(f"✅ PaddleOCR检测成功")
            print(f"   旋转角度: {paddleocr_result['angle']}°")
            print(f"   置信度: {paddleocr_result['confidence']:.3f}")
            print(f"   类别ID: {paddleocr_result['class_id']}")
            print(f"   标签: {paddleocr_result['label_name']}")
            print(f"   总时间: {paddleocr_time:.3f}s")
            if 'inference_time' in paddleocr_result:
                print(f"   推理时间: {paddleocr_result['inference_time']:.3f}s")
            if 'optimization_used' in paddleocr_result:
                opt = paddleocr_result['optimization_used']
                print(f"   优化级别: {opt['level']}")
        else:
            print(f"❌ PaddleOCR检测失败: {paddleocr_result['error_msg']}")
            print(f"   总时间: {paddleocr_time:.3f}s")
            
    except Exception as e:
        print(f"❌ PaddleOCR初始化失败: {e}")
        paddleocr_result = None
    
    print()
    
    # 比较结果
    print("📈 方法比较")
    print("-" * 30)
    
    if tesseract_result and tesseract_result['success'] and paddleocr_result and paddleocr_result['success']:
        print(f"Tesseract: {tesseract_result['angle']}° (置信度: {tesseract_result['confidence']:.3f}, 时间: {tesseract_time:.3f}s)")
        print(f"PaddleOCR: {paddleocr_result['angle']}° (置信度: {paddleocr_result['confidence']:.3f}, 时间: {paddleocr_time:.3f}s)")
        
        if tesseract_result['angle'] == paddleocr_result['angle']:
            print("✅ 两种方法检测结果一致")
        else:
            print("⚠️  两种方法检测结果不一致")
            
        if paddleocr_time < tesseract_time:
            speedup = tesseract_time / paddleocr_time
            print(f"🚀 PaddleOCR比Tesseract快 {speedup:.1f}x")
        else:
            slowdown = paddleocr_time / tesseract_time
            print(f"🐌 PaddleOCR比Tesseract慢 {slowdown:.1f}x")
    
    elif tesseract_result and tesseract_result['success']:
        print("✅ 只有Tesseract检测成功")
        
    elif paddleocr_result and paddleocr_result['success']:
        print("✅ 只有PaddleOCR检测成功")
        
    else:
        print("❌ 两种方法都检测失败")


def test_multiple_images():
    """测试多个图像"""
    test_images = [
        "/Users/<USER>/Coding/rh-ocr/cut-images/2025/07/25/rotate_11_00_55.png",
        # 可以添加更多测试图像
    ]
    
    for i, image_path in enumerate(test_images):
        if i > 0:
            print("\n" + "=" * 80 + "\n")
        test_rotation_methods(image_path)


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # 测试指定图像
        image_path = sys.argv[1]
        test_rotation_methods(image_path)
    else:
        # 测试默认图像
        test_multiple_images()
