import base64
import cv2
import numpy as np
from fastapi import FastAPI, HTTPException
#from rapidocr import RapidOCR
from pathlib import Path
import time
from pydantic import BaseModel
from logger_config import setup_logger
import logging
import argparse
import sys
import os
from datetime import datetime

from inference.ocr_engine import OCREngine
from rotation_detector import RotationDetector, detect_image_rotation
from doc_orientation_classifier import DocOrientationClassifier, detect_doc_orientation
from image_saver import save_image_in_background, generate_unique_filename, generate_date_path

import pymupdf

# 解析命令行参数
def parse_args():
    parser = argparse.ArgumentParser(description='OCR Server')
    parser.add_argument('--inference_engine', type=str, default='openvino',
                        choices=['onnx', 'openvino'],
                        help='推理引擎类型 (默认: openvino)')
    parser.add_argument('--use_gpu', action='store_true', default=False,
                        help='使用GPU加速')
    parser.add_argument('--use_angle_cls', action='store_true', default=False,
                        help='使用角度分类器')
    parser.add_argument('--rotation_method', type=str, default='tesseract',
                        choices=['tesseract', 'paddleocr'],
                        help='旋转检测方法 (默认: tesseract, 可选: paddleocr)')
    parser.add_argument('--rotation_confidence', type=float, default=0.5,
                        help='旋转检测置信度阈值 (默认: 0.5)')
    parser.add_argument('--host', type=str, default='0.0.0.0',
                        help='服务器主机地址')
    parser.add_argument('--port', type=int, default=8000,
                        help='服务器端口')
    
    # 从环境变量读取参数（用于从start.sh传递参数）
    env_args = os.environ.get('OCR_SERVER_ARGS', '')
    if env_args:
        # 解析环境变量中的参数
        env_args_list = env_args.split()
        args = parser.parse_args(env_args_list)
    elif len(sys.argv) == 1:
        # 没有命令行参数，使用默认值
        args = parser.parse_args([])
    else:
        # 有命令行参数，直接解析
        args = parser.parse_args()
    
    return args

# 获取命令行参数
args = parse_args()

ocr_server = FastAPI()
logging.getLogger().handlers.clear()
logger = setup_logger()

# 将 uvicorn 的日志器配置为使用你的自定义处理器
uvicorn_logger = logging.getLogger("uvicorn")
uvicorn_logger.handlers = logger.handlers
# engine = RapidOCR(config_path="default_rapidocr.yaml")

# 根据参数初始化模型
import os
worker_id = os.getpid()
logger.info(f"Worker[{worker_id}] 初始化OCR模型 - 引擎:{args.inference_engine}, GPU:{args.use_gpu}, 角度分类器:{args.use_angle_cls}")

model = OCREngine(
    use_angle_cls=args.use_angle_cls, 
    use_gpu=args.use_gpu,
    inference_engine=args.inference_engine
)

# 初始化旋转检测器
rotation_detector = None
doc_orientation_classifier = None

if args.rotation_method == 'tesseract':
    logger.info(f"Worker[{worker_id}] 初始化Tesseract旋转检测器")
    try:
        # 使用性能优化配置
        # - confidence_threshold=1.0: 实际PDF页面置信度通常在1.0-2.0之间
        # - optimization_level="high": 使用高度优化，平衡速度和准确性
        # - max_image_size=1000: 增大以适应PDF页面检测
        # - enable_roi_detection=False: 禁用ROI检测，PDF页面通常整页都是内容
        rotation_detector = RotationDetector(
            confidence_threshold=1.0,
            optimization_level="high",
            max_image_size=1000,
            enable_roi_detection=False
        )
        logger.info(f"Worker[{worker_id}] Tesseract旋转检测器初始化完成 - 优化级别: high")
    except Exception as e:
        logger.error(f"Worker[{worker_id}] Tesseract旋转检测器初始化失败: {e}")
        logger.info(f"Worker[{worker_id}] 系统将继续运行，但不进行旋转检测")

elif args.rotation_method == 'paddleocr':
    logger.info(f"Worker[{worker_id}] 初始化PaddleOCR文档方向分类器")
    try:
        doc_orientation_classifier = DocOrientationClassifier(
            confidence_threshold=args.rotation_confidence
        )
        logger.info(f"Worker[{worker_id}] PaddleOCR文档方向分类器初始化完成 - 置信度阈值: {args.rotation_confidence}")
    except Exception as e:
        logger.error(f"Worker[{worker_id}] PaddleOCR文档方向分类器初始化失败: {e}")
        logger.info(f"Worker[{worker_id}] 系统将继续运行，但不进行旋转检测")

logger.info(f"Worker[{worker_id}] OCR模型初始化完成")

STANDARD_DPI = 72
DEFAULT_DPI = 300


@ocr_server.get("/")
def read_root():
    logger.info("Hello, OCR Server!")
    return {"message": "Hello, OCR Server!"}


class PathInput(BaseModel):
    path: str


class MatchArea(BaseModel):
    page: int  # 裁切的页码
    startX: int  # 裁切的起始坐标x
    startY: int  # 裁切的起始坐标y
    cutHeight: int  # 裁切高度
    cutWidth: int  # 裁切宽度
    enableRotationDetection: bool = False  # 是否开启旋转检测，默认开启


class OCRRequest(BaseModel):
    images: list[str]
    dataType: str = "pdf"
    matchArea: MatchArea = None
    path: Path = None

    def __str__(self) -> str:
        """自定义字符串表示，忽略images字段并包含类名"""
        # 创建一个不包含images的字典
        data_dict = self.model_dump()
        if "images" in data_dict:
            data_dict["images"] = "[images data omitted]"

        # 返回包含类名的字符串
        return f"{self.__class__.__name__}{data_dict}"

    # 如果需要同时自定义repr
    def __repr__(self) -> str:
        return self.__str__()


def ocr_from_path(path_str):
    path = Path(path_str)

    if not path.exists():
        raise HTTPException(status_code=400, detail="Path does not exist.")

    if path.is_file():
        # 如果是文件
        img = cv2.imread(path_str)
        return do_ocr(img, path_str)

    elif path.is_dir():
        # 如果是文件夹，遍历所有文件路径
        for file in path.iterdir():
            if file.is_file():
                img = cv2.imread(path_str)
                return do_ocr(img, path_str)
    else:
        # 如果既不是文件也不是文件夹
        raise HTTPException(status_code=400, detail="Invalid path type.")




def detect_pdf_rotation(page):
    """
    检测PDF页面的旋转角度

    Args:
        page: PyMuPDF页面对象

    Returns:
        int: 检测到的旋转角度（度）
    """
    detected_rotation_angle = 0

    # 检查是否有可用的旋转检测器
    if rotation_detector is None and doc_orientation_classifier is None:
        logger.info("没有可用的旋转检测器，跳过旋转检测")
        return detected_rotation_angle

    try:
        rotation_start_time = time.time()

        # 先获取整个页面的图像进行旋转检测，使用更低DPI以提高检测速度
        # 使用120 DPI进行旋转检测，配合图像预处理优化，能大幅提升速度
        temp_pix = page.get_pixmap(dpi=120)
        temp_img_bytes = temp_pix.tobytes("png")
        temp_np_arr = np.frombuffer(temp_img_bytes, np.uint8)
        temp_img = cv2.imdecode(temp_np_arr, cv2.IMREAD_COLOR)

        if temp_img is not None:
            # 保存旋转检测的临时图片用于调试
            debug_filename = generate_unique_filename(prefix="rotate")
            save_image_in_background(temp_img, debug_filename)

            # 根据配置选择旋转检测方法
            if args.rotation_method == 'tesseract' and rotation_detector is not None:
                # 使用Tesseract OSD进行旋转检测
                rotation_result = rotation_detector.detect_rotation(temp_img)
                method_name = "Tesseract OSD"
            elif args.rotation_method == 'paddleocr' and doc_orientation_classifier is not None:
                # 使用PaddleOCR文档方向分类器进行旋转检测
                rotation_result = doc_orientation_classifier.detect_rotation(temp_img)
                method_name = "PaddleOCR文档方向分类器"
            else:
                logger.error("没有可用的旋转检测器与配置的方法匹配")
                return detected_rotation_angle

            logger.info(f"rotation result: {rotation_result}")
            rotation_end_time = time.time()

            detected_rotation_angle = rotation_result['angle']

            # 构建性能日志信息
            perf_info = f"旋转检测用时: {rotation_end_time - rotation_start_time:.3f}s"
            if 'osd_time' in rotation_result:
                perf_info += f", OSD用时: {rotation_result['osd_time']:.3f}s"
            if 'inference_time' in rotation_result:
                perf_info += f", 推理用时: {rotation_result['inference_time']:.3f}s"
            if 'optimization_used' in rotation_result:
                opt = rotation_result['optimization_used']
                perf_info += f", 优化: {opt['level']}"
                if opt['image_resized']:
                    perf_info += "(缩放)"
                if opt['roi_detected']:
                    perf_info += "(ROI)"

            logger.info(f"使用{method_name}检测到PDF内容旋转角度: {detected_rotation_angle}°, "
                           f"置信度: {rotation_result['confidence']:.2f}, {perf_info}")
        else:
            logger.error("temp_img为空，无法从PDF页面生成图像进行旋转检测")

    except Exception as e:
        logger.error(f"PDF内容旋转检测时出错: {e}")
        logger.error("旋转检测失败，将继续使用默认方向处理")

    return detected_rotation_angle

def get_img_from_area(file_bytes, ocr_request: OCRRequest):
    match_area = ocr_request.matchArea
    start_time = time.time()
    doc = pymupdf.open(stream=file_bytes)
    page_num = 0
    if match_area is not None:
        page_num = match_area.page
    page = doc[page_num]
    if ocr_request.dataType == "pdf":

        # 根据MatchArea中的字段判断是否进行旋转检测
        if match_area.enableRotationDetection:
            logger.info("开始旋转检测")
            logger.info(f"页面旋转属性：{page.rotation}")
            page.set_rotation(0)
            detected_rotation_angle = detect_pdf_rotation(page)
            page.set_rotation(-detected_rotation_angle)

        scale = DEFAULT_DPI / STANDARD_DPI
        start_x = int(match_area.startX / scale)
        start_y = int(match_area.startY / scale)
        logger.info(f"page rect width: {page.rect.width}, page rect height: {page.rect.height}")
        end_x = int(page.rect.width if match_area.cutWidth < 0 else (start_x + match_area.cutWidth) / scale)
        end_y = int(page.rect.height if match_area.cutHeight < 0 else (start_y + match_area.cutHeight) /scale)
        rotated_rect = pymupdf.Rect(start_x, start_y, end_x, end_y)
        orig_rect = rotated_rect * page.derotation_matrix
        page.set_cropbox(orig_rect)

    pix = page.get_pixmap(dpi=DEFAULT_DPI)
    img_bytes = pix.tobytes("png")
    np_arr = np.frombuffer(img_bytes, np.uint8)
    img = cv2.imdecode(np_arr, cv2.IMREAD_COLOR)
    end_time = time.time()
    logger.info(f"pdf to image takes time: {end_time - start_time:.3f} s")
    
    # 异步保存图片到 cut-images 文件夹
    # 使用新模块生成唯一文件名并保存
    filename = generate_unique_filename(prefix="cut", page_num=page_num)
    save_image_in_background(img, filename)
    
    return img


def do_ocr(img, request_info: str = None):
    """
    执行OCR识别，支持Path对象或base64字符串
    """
    try:
        start_time = time.time()

        raw_result = model.ocr(img=img, cls=False)
        # raw_result = engine(img)
        end_time = time.time()

        logger.info(f"Finish processing {request_info} ocr takes time: {end_time - start_time:.3f} s")
        # logger.info(f"Raw result: {raw_result}")
        # 针对ONNXOCR转换数据结构
        result = []
        if raw_result and len(raw_result) > 0:
            for item in raw_result[0]:
                box = item[0]  # 坐标点列表 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
                text = item[1][0]  # 识别的文本

                # 构建新的数据结构
                coordinates = []
                for point in box:
                    coordinates.append({"x": point[0], "y": point[1]})

                result.append({
                    "content": text,
                    "coordinate": coordinates
                })

        logger.info(f"Processed result: {result}")

        return result, None
    except Exception as e:
        error_msg = f"Error processing {request_info}: {e}"
        logger.error(error_msg)
        return None, error_msg


@ocr_server.post("/read-images")
def read_images(input_data: PathInput):
    start_time = time.time()
    result = ocr_from_path(input_data.path)
    end_time = time.time()
    return {"totalTime": f"{end_time - start_time:.3f} s", "result": result}


@ocr_server.post("/ocr/prediction")
def ocr_prediction(ocr_request: OCRRequest):
    ocr_results = []
    
    for base64_img in ocr_request.images:
        img = get_img_from_area(base64.b64decode(base64_img), ocr_request)
        result, err_msg = do_ocr(img, str(ocr_request))
        ocr_results.append({
            "result": result,
            "errMsg": err_msg
        })
    
    return {
        "ocrResults": ocr_results
    }