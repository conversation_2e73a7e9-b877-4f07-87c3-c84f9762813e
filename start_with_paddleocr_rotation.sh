#!/bin/bash

# 使用PaddleOCR文档方向分类器启动OCR服务器
# 相比Tesseract OSD，PaddleOCR方法速度更快，准确性相当
# 现在使用重构后的inference/predict_ori.py模块

echo "🚀 启动OCR服务器 - 使用PaddleOCR文档方向分类器"
echo "================================================"
echo "旋转检测方法: PaddleOCR文档方向分类器 (inference/predict_ori.py)"
echo "置信度阈值: 0.3"
echo "推理引擎: ONNX (统一引擎系统)"
echo "端口: 8000"
echo ""

python ocr_server.py \
    --rotation_method paddleocr \
    --rotation_confidence 0.3 \
    --inference_engine onnx \
    --host 0.0.0.0 \
    --port 8000

echo ""
echo "服务器已停止"
